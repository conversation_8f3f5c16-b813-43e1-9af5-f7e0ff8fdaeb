//! Comprehensive comparison test between Go and Rust implementations
//!
//! This example tests various application IDs and compares outputs with the Go implementation

use get_machineid::{id, protected_id};

fn main() {
    println!("=== Rust Library API Test ===");
    
    // Test raw ID
    println!("\n1. Raw Machine ID:");
    match id() {
        Ok(machine_id) => println!("   {}", machine_id),
        Err(e) => println!("   Error: {}", e),
    }
    
    // Test protected IDs with various app IDs (same as Go test)
    let test_cases = vec![
        "TestApp",
        "MyAwesomeApp", 
        "app.with.dots-and_underscores",
        "",
        "🦀 rust app 🦀",
        "a very long application identifier that contains many characters to test edge cases",
    ];
    
    println!("\n2. Protected Machine IDs:");
    for (i, app_id) in test_cases.iter().enumerate() {
        match protected_id(app_id) {
            Ok(protected) => {
                println!("   {}. App ID: '{}'", i + 1, app_id);
                println!("      Protected ID: {}", protected);
            }
            Err(e) => {
                println!("   {}. Error with app ID '{}': {}", i + 1, app_id, e);
            }
        }
    }
    
    println!("\n=== Additional Rust-specific Tests ===");
    
    // Test edge cases
    let edge_cases = vec![
        "simple",
        "with spaces",
        "with-dashes",
        "with_underscores",
        "with.dots",
        "with/slashes",
        "with@special#chars$",
        "UPPERCASE",
        "MiXeD_CaSe",
        "123numbers456",
        "unicode-测试-тест-🚀",
    ];
    
    println!("\n3. Edge Case Testing:");
    for (i, app_id) in edge_cases.iter().enumerate() {
        match protected_id(app_id) {
            Ok(protected) => {
                println!("   {}. '{}' -> {}", i + 1, app_id, protected);
            }
            Err(e) => {
                println!("   {}. Error with '{}': {}", i + 1, app_id, e);
            }
        }
    }
}
