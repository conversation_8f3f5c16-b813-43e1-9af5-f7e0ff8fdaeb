//! # get_machineid
//!
//! Cross-platform library for reading unique machine IDs without admin privileges.
//!
//! This crate provides support for reading the unique machine ID of most host operating systems
//! without requiring administrator privileges. It's a Rust port of the Go library
//! [machineid](https://github.com/denisbrodbeck/machineid).
//!
//! ## Features
//!
//! - Cross-platform support (Linux, macOS, Windows, BSD)
//! - No admin privileges required
//! - Hardware independent (no MAC, BIOS, or CPU usage)
//! - Cryptographically secure hashed IDs
//! - IDs are unique to the installed OS
//!
//! ## Usage
//!
//! ```rust
//! use get_machineid::{id, protected_id};
//!
//! // Get the raw machine ID
//! let machine_id = id().expect("Failed to get machine ID");
//! println!("Machine ID: {}", machine_id);
//!
//! // Get a cryptographically secure hash of the machine ID
//! let app_id = "my_app_name";
//! let protected_machine_id = protected_id(app_id).expect("Failed to get protected ID");
//! println!("Protected ID: {}", protected_machine_id);
//! ```
//!
//! ## Security Considerations
//!
//! A machine ID uniquely identifies the host. Therefore it should be considered "confidential",
//! and must not be exposed in untrusted environments. If you need a stable unique identifier
//! for your app, do not use the machine ID directly.
//!
//! Instead, use the `protected_id` function which returns a cryptographically secure hash
//! of the machine ID using HMAC-SHA256, keyed by the machine ID and your application identifier.

use hmac::{Hmac, Mac};
use sha2::Sha256;
use std::error::Error;
use std::fmt;

type HmacSha256 = Hmac<Sha256>;

/// Error type for machine ID operations
#[derive(Debug)]
pub struct MachineIdError {
    message: String,
}

impl fmt::Display for MachineIdError {
    fn fmt(&self, f: &mut fmt::Formatter) -> fmt::Result {
        write!(f, "machineid: {}", self.message)
    }
}

impl Error for MachineIdError {}

impl From<std::io::Error> for MachineIdError {
    fn from(err: std::io::Error) -> Self {
        MachineIdError {
            message: err.to_string(),
        }
    }
}

impl From<String> for MachineIdError {
    fn from(message: String) -> Self {
        MachineIdError { message }
    }
}

impl From<&str> for MachineIdError {
    fn from(message: &str) -> Self {
        MachineIdError {
            message: message.to_string(),
        }
    }
}

/// Returns the platform-specific machine ID of the current host OS.
///
/// Regard the returned ID as "confidential" and consider using `protected_id()` instead.
///
/// # Examples
///
/// ```rust
/// use get_machineid::id;
///
/// match id() {
///     Ok(machine_id) => println!("Machine ID: {}", machine_id),
///     Err(e) => eprintln!("Error: {}", e),
/// }
/// ```
///
/// # Errors
///
/// Returns a `MachineIdError` if the machine ID cannot be read from the system.
pub fn id() -> Result<String, MachineIdError> {
    machine_id()
}

/// Returns a hashed version of the machine ID in a cryptographically secure way,
/// using a fixed, application-specific key.
///
/// Internally, this function calculates HMAC-SHA256 of the application ID, keyed by the machine ID.
///
/// # Arguments
///
/// * `app_id` - A string slice that holds the application identifier
///
/// # Examples
///
/// ```rust
/// use get_machineid::protected_id;
///
/// match protected_id("my_app_name") {
///     Ok(protected_machine_id) => println!("Protected ID: {}", protected_machine_id),
///     Err(e) => eprintln!("Error: {}", e),
/// }
/// ```
///
/// # Errors
///
/// Returns a `MachineIdError` if the machine ID cannot be read from the system.
pub fn protected_id(app_id: &str) -> Result<String, MachineIdError> {
    let machine_id = id()?;
    Ok(protect(app_id, &machine_id))
}

/// Calculates HMAC-SHA256 of the application ID, keyed by the machine ID and returns a hex-encoded string.
fn protect(app_id: &str, machine_id: &str) -> String {
    let mut mac = HmacSha256::new_from_slice(machine_id.as_bytes())
        .expect("HMAC can take key of any size");
    mac.update(app_id.as_bytes());
    hex::encode(mac.finalize().into_bytes())
}

/// Trims whitespace and newlines from a string
fn trim(s: &str) -> String {
    s.trim().trim_end_matches('\n').to_string()
}

// Platform-specific implementations
#[cfg(target_os = "linux")]
mod platform;

#[cfg(target_os = "macos")]
mod platform;

#[cfg(target_os = "windows")]
mod platform;

#[cfg(any(
    target_os = "freebsd",
    target_os = "netbsd",
    target_os = "openbsd",
    target_os = "dragonfly"
))]
mod platform;

use platform::machine_id;

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_id() {
        let result = id();
        assert!(result.is_ok(), "Failed to get machine ID: {:?}", result);
        let machine_id = result.unwrap();
        assert!(!machine_id.is_empty(), "Got empty machine ID");
    }

    #[test]
    fn test_protected_id() {
        let machine_id = id().expect("Failed to get machine ID");
        let protected = protected_id("app.id").expect("Failed to get protected ID");
        
        assert!(!protected.is_empty(), "Got empty protected ID");
        assert_ne!(machine_id, protected, "ID and protected ID are the same");
        
        // Test that the same app_id produces the same protected ID
        let protected2 = protected_id("app.id").expect("Failed to get protected ID");
        assert_eq!(protected, protected2, "Protected IDs should be consistent");
    }

    #[test]
    fn test_protect() {
        let app_id = "ms.azur.appX";
        let machine_id = "1a1238d601ad430cbea7efb0d1f3d92d";
        let hash = protect(app_id, machine_id);
        
        assert!(!hash.is_empty(), "Hash is empty");
        
        // Verify the hash is valid hex
        let decoded = hex::decode(&hash);
        assert!(decoded.is_ok(), "Hash is not valid hex");
        
        // Verify HMAC
        let mut mac = HmacSha256::new_from_slice(machine_id.as_bytes()).unwrap();
        mac.update(app_id.as_bytes());
        let expected = hex::encode(mac.finalize().into_bytes());
        assert_eq!(hash, expected, "HMAC verification failed");
    }

    #[test]
    fn test_trim() {
        assert_eq!(trim(""), "");
        assert_eq!(trim(" space "), "space");
        assert_eq!(trim("data\n"), "data");
        assert_eq!(trim(" some data \n"), "some data");
    }
}
