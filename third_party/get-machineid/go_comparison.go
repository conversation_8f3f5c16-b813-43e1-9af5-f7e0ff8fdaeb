package main

import (
	"fmt"
	"log"
	"os"
	"path/filepath"
)

// Import the local machineid package
import "../get-machineid-proj/machineid-master"

func main() {
	fmt.Println("=== Go Library API Test ===")
	
	// Test raw ID
	fmt.Println("\n1. Raw Machine ID:")
	id, err := machineid.ID()
	if err != nil {
		log.Fatalf("Error getting raw ID: %v", err)
	}
	fmt.Printf("   %s\n", id)
	
	// Test protected IDs with various app IDs
	testCases := []string{
		"TestApp",
		"MyAwesomeApp", 
		"app.with.dots-and_underscores",
		"",
		"🦀 rust app 🦀",
		"a very long application identifier that contains many characters to test edge cases",
	}
	
	fmt.Println("\n2. Protected Machine IDs:")
	for i, appID := range testCases {
		protectedID, err := machineid.ProtectedID(appID)
		if err != nil {
			fmt.Printf("   %d. Error with app ID '%s': %v\n", i+1, appID, err)
		} else {
			fmt.Printf("   %d. App ID: '%s'\n", i+1, appID)
			fmt.Printf("      Protected ID: %s\n", protectedID)
		}
	}
}
