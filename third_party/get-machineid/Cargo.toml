[package]
name = "get_machineid"
version = "0.1.0"
edition = "2021"
authors = ["Your Name <<EMAIL>>"]
description = "Cross-platform library for reading unique machine IDs without admin privileges"
license = "MIT"
repository = "https://github.com/yourusername/get_machineid"
keywords = ["machine-id", "uuid", "cross-platform", "system", "identification"]
categories = ["os", "api-bindings"]
readme = "README.md"

[dependencies]
# Core cryptographic dependencies for HMAC-SHA256
hmac = "0.12"
sha2 = "0.10"
hex = "0.4"

# CLI dependencies
clap = { version = "4.4", features = ["derive"] }

# Platform-specific dependencies
[target.'cfg(windows)'.dependencies]
winreg = "0.52"

[target.'cfg(unix)'.dependencies]
# No additional dependencies needed for Unix platforms

[dev-dependencies]
# Test dependencies
tempfile = "3.0"

[lib]
name = "get_machineid"
path = "src/lib.rs"

[[bin]]
name = "get_machineid"
path = "src/main.rs"

[[example]]
name = "basic_usage"
path = "examples/basic_usage.rs"

[[example]]
name = "protected_id"
path = "examples/protected_id.rs"

[[example]]
name = "benchmark"
path = "examples/benchmark.rs"

[[example]]
name = "cli_demo"
path = "examples/cli_demo.rs"

[[example]]
name = "comparison_test"
path = "examples/comparison_test.rs"
