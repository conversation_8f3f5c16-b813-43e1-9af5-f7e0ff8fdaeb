use anyhow::{anyhow, Result};
use base64;
use chrono::Local;
use futures::future::join_all;
use futures::StreamExt;
use html_escape;
use llm::{
    builder::{LLMBackend, LLMBuilder as LLMBuilderTrait}, // 修复LLMBuilder引用
    chat::{ChatMessage, ImageMime, StructuredOutputFormat}, // Chat-related structures
};
use regex;
use repair_json;
use rinf::debug_print;
use serde::{Deserialize, Serialize};
use serde_json::json;
use std::collections::HashMap;
use std::env;
use std::error::Error as StdError;
use std::fs;
use std::io::Write;
use std::path::Path;
use std::sync::Arc;
use text_splitter::TextSplitter;

// fn extract_json(response: &str) -> Result<String> {
//     let json_start = response.find("```json").unwrap_or(0);
//     let json_end = response.rfind("```").unwrap_or(response.len());
//     let json_str = &response[json_start..json_end];
//     Ok(json_str.to_string())
// }

fn extract_json(text: &str) -> Result<String> {
    // 1. 去除整体的前后空白
    let mut s = text.trim();

    // 2. 检查并去除 "```json" 前缀
    //    `strip_prefix` 返回 Option<&str>
    //    如果匹配，`map_or` 会对 Some(rest) 执行闭包 `|rest| rest.trim_start()`
    //    如果不匹配 (None)，`map_or` 会返回默认值 `s` (即未修改的 s)
    s = s
        .strip_prefix("```json")
        .map_or(s, |rest| rest.trim_start());

    // 3. 检查并去除 "```" 后缀
    //    逻辑与 `strip_prefix` 类似
    s = s.strip_suffix("```").map_or(s, |rest| rest.trim_end());

    // 4. 返回处理后的字符串
    Ok(s.trim().to_string())
}

/// 构建LLM客户端的辅助函数
pub fn build_llm_client(
    backend: &str,
    model: &str,
    api_key: &str,
    base_url: Option<&str>,
    system_prompt: &str,
    temperature: f32,
    max_tokens: Option<u32>,
    top_p: Option<f32>,
    top_k: Option<u32>,
    reasoning: Option<bool>,
    timeout: Option<u32>,
) -> anyhow::Result<LLMBuilderTrait> {
    // 确定后端类型
    let llm_backend = match backend.to_lowercase().as_str() {
        "openai" => LLMBackend::OpenAI,
        "anthropic" => LLMBackend::Anthropic,
        "google" => LLMBackend::Google,
        "deepseek" => LLMBackend::DeepSeek,
        "xai" => LLMBackend::XAI,
        "phind" => LLMBackend::Phind,
        "groq" => LLMBackend::Groq,
        "azure" => LLMBackend::AzureOpenAI,
        "ollama" => LLMBackend::Ollama,
        "elevenlabs" => LLMBackend::ElevenLabs,
        _ => return Err(anyhow!("不支持的LLM后端: {}", backend)),
    };
    debug_print!("llm_backend: {:?}", &llm_backend);
    let schema_str = r#"{
        "name": "anki_card",
        "schema": {
          "type": "array",
          "items": {
            "type": "object",
            "properties": {
              "fields": {
                "type": "object",
                "description": "卡片的字段，根据卡片类型不同而有所差异",
                "additionalProperties": true
              },
              "tags": {
                "type": "array",
                "items": {
                  "type": "string"
                },
                "description": "包含3-5个与该卡片知识点直接相关的关键词标签"
              },
              "type": {
                "type": "string",
                "enum": ["qa", "cloze", "choice", "multi_choice", "judge"],
                "description": "卡片类型"
              }
            },
            "required": ["fields", "tags", "type"]
          }
        }
      }
    "#;
    let schema: StructuredOutputFormat = serde_json::from_str(schema_str)?;

    // 构建LLM客户端
    let mut builder = LLMBuilderTrait::new().backend(llm_backend);
    if let Some(url) = base_url {
        builder = builder.base_url(url);
    }
    builder = builder
        .system(system_prompt)
        .api_key(api_key)
        .model(model)
        .temperature(temperature)
        // .schema(schema)
        .stream(false);

    // 设置推理
    if let Some(reasoning) = reasoning {
        builder = builder.reasoning(reasoning);
    }

    // 设置超时
    if let Some(timeout_secs) = timeout {
        builder = builder.timeout_seconds(timeout_secs as u64);
    }

    // 设置最大令牌数
    if let Some(tokens) = max_tokens {
        builder = builder.max_tokens(tokens);
    }

    // 设置top_p参数
    if let Some(p) = top_p {
        builder = builder.top_p(p);
    }

    // 设置top_k参数
    if let Some(k) = top_k {
        builder = builder.top_k(k as u32);
    }
    // 返回构建器
    Ok(builder)
}

/// 处理附件并将它们分成批次
async fn process_attachments(
    paths: Vec<String>,
    chunk_size: usize,
    max_concurrent: usize,
    page_range: Option<&str>,
) -> anyhow::Result<Vec<Vec<String>>> {
    let mut text_files = Vec::new();
    let mut image_files = Vec::new();
    let mut pdf_files = Vec::new();
    let mut docx_files = Vec::new();
    let mut temp_files = Vec::new(); // 存储需要在处理完成后删除的临时文件

    // 获取临时目录
    let temp_dir = crate::anki::utils::get_temp_dir().await?;
    debug_print!("临时目录: {}", temp_dir);

    // 分类文件
    for path in paths {
        let path_lower = path.to_lowercase();
        if path_lower.ends_with(".pdf") {
            pdf_files.push(path);
        } else if path_lower.ends_with(".docx") {
            docx_files.push(path);
        } else if path_lower.ends_with(".txt")
            || path_lower.ends_with(".md")
            || path_lower.ends_with(".html")
        {
            text_files.push(path);
        } else if path_lower.ends_with(".jpg")
            || path_lower.ends_with(".jpeg")
            || path_lower.ends_with(".png")
            || path_lower.ends_with(".webp")
        {
            image_files.push(path);
        }
    }

    let mut batches = Vec::new();

    // 处理PDF文件 - 将其转换为图片
    for pdf_path in &pdf_files {
        debug_print!("处理PDF文件: {}", pdf_path);
        let pdf_temp_dir = format!("{}/anki_guru_pdf_{}", temp_dir, uuid::Uuid::new_v4());
        fs::create_dir_all(&pdf_temp_dir)?;

        // 将PDF转换为图片
        let page_range_str = page_range.unwrap_or("");
        match crate::anki::pdf_utils::export_pdf_to_images_by_pdfium(
            pdf_path,
            &pdf_temp_dir,
            "jpg",
            page_range_str,
            None,
            false,
            |_, _, _| {},
        ) {
            Ok(_) => {
                // 读取生成的图片文件
                if let Ok(entries) = fs::read_dir(&pdf_temp_dir) {
                    for entry in entries.filter_map(Result::ok) {
                        let img_path = entry.path().to_string_lossy().to_string();
                        if img_path.to_lowercase().ends_with(".jpg") {
                            image_files.push(img_path.clone());
                            temp_files.push(img_path); // 添加到临时文件列表
                        }
                    }
                }
                temp_files.push(pdf_temp_dir); // 添加临时目录以便后续删除
            }
            Err(e) => {
                debug_print!("PDF转图片失败: {}", e);
            }
        }
    }

    // 处理DOCX文件 - 先转换为HTML，再转换为TXT
    for docx_path in &docx_files {
        debug_print!("处理DOCX文件: {}", docx_path);
        let temp_html_path = format!("{}/anki_guru_docx_{}.html", temp_dir, uuid::Uuid::new_v4());
        let temp_txt_path = format!("{}/anki_guru_docx_{}.txt", temp_dir, uuid::Uuid::new_v4());

        // 将DOCX转换为HTML
        let docx_result =
            crate::anki::docx_card::convert_docx2html(docx_path, &temp_html_path, true);

        // 提前处理错误，避免跨await使用
        if docx_result.is_err() {
            debug_print!("DOCX转HTML失败: {}", docx_result.unwrap_err());
            continue;
        }

        // 读取HTML内容
        let html_content = match fs::read_to_string(&temp_html_path) {
            Ok(content) => content,
            Err(e) => {
                debug_print!("读取HTML文件失败: {}", e);
                continue;
            }
        };

        // 只有当HTML内容不为空时才进行转换
        if !html_content.is_empty() {
            let text_content = match crate::anki::utils::html_to_text(&html_content).await {
                Ok(text) => text,
                Err(e) => {
                    // 将错误转换为字符串，避免传递非Send类型
                    debug_print!("HTML转TXT失败: {}", e);
                    continue;
                }
            };
            debug_print!("text_content: {:?}", &text_content);
            // 写入TXT文件
            if fs::write(&temp_txt_path, text_content.as_bytes()).is_ok() {
                text_files.push(temp_txt_path.clone());
                temp_files.push(temp_txt_path); // 添加到临时文件列表
            }
        }

        temp_files.push(temp_html_path); // 添加临时HTML文件以便后续删除
    }

    // 处理文本文件
    if !text_files.is_empty() {
        // 合并所有文本文件的内容
        let mut all_text = String::new();
        for path in &text_files {
            match fs::read_to_string(path) {
                Ok(content) => {
                    let file_name = Path::new(path)
                        .file_name()
                        .map_or("文件", |name| name.to_str().unwrap_or("文件"));

                    all_text.push_str(&format!("\n\n=== {} 开始 ===\n\n", file_name));
                    all_text.push_str(&content);
                    all_text.push_str(&format!("\n\n=== {} 结束 ===\n\n", file_name));
                }
                Err(e) => {
                    debug_print!("无法读取文本文件 {}: {}", path, e);
                }
            }
        }

        // 使用text-splitter分割文本
        if !all_text.is_empty() {
            let splitter = TextSplitter::new(chunk_size);

            let chunks: Vec<&str> = splitter.chunks(&all_text).collect();
            debug_print!("文本分割为 {} 个块", chunks.len());

            // 为每个文本块创建一个批次
            for chunk in chunks {
                let temp_file_path = format!(
                    "{}/anki_guru_text_chunk_{}.txt",
                    temp_dir,
                    uuid::Uuid::new_v4()
                );
                fs::write(&temp_file_path, chunk)?;
                batches.push(vec![temp_file_path.clone()]);
                temp_files.push(temp_file_path); // 添加到临时文件列表
            }
        }
    }

    // 处理图片文件，每个图片单独一个批次
    for image_path in image_files {
        batches.push(vec![image_path]);
    }

    // 如果没有批次，添加一个空批次以确保至少执行一次请求
    if batches.is_empty() {
        batches.push(vec![]);
    }

    // 如果批次数量超过最大并发数，考虑优化批次分配
    if batches.len() > max_concurrent && max_concurrent > 1 {
        debug_print!(
            "批次数量({})超过最大并发数({}), 进行优化",
            batches.len(),
            max_concurrent
        );

        // 这里可以实现更复杂的批次合并逻辑
        // 例如，将多个小批次合并为一个大批次，以减少总批次数
        // 或者根据文件大小和类型进行更智能的分配
    }

    // 删除临时文件
    // 注意：这里我们不删除临时文件，因为它们可能还在使用中
    // 可以在程序结束时或者在一个单独的清理任务中删除

    Ok(batches)
}

/// 解析LLM响应并提取卡片数据
pub async fn parse_card_response(
    response: &serde_json::Value,
    parent_deck: &str,
    tags: &[String],
    card_type_models: &HashMap<String, String>,
    cloze_mode: &str,
    is_per_cloze_per_card: bool,
    is_transform_formula: bool,
    is_html_escape: bool,
) -> Vec<crate::anki::models::AnkiNote> {
    let mut notes = Vec::new();

    // 统一处理卡片数组格式
    if let Some(cards) = response.as_array() {
        for card in cards {
            // 获取卡片类型
            let card_type = card["type"].as_str().unwrap_or("qa");
            debug_print!("card_type: {:?}", &card_type);
            // 检查卡片类型是否有对应的模型名称
            if let Some(model_name) = card_type_models.get(card_type) {
                let mut fields = Vec::new();

                // 获取卡片标签
                let mut card_tags = tags.to_vec();
                if let Some(card_tags_array) = card["tags"].as_array() {
                    for tag in card_tags_array {
                        if let Some(tag_str) = tag.as_str() {
                            // 将标签中的空格替换为下划线
                            let processed_tag = tag_str.replace(" ", "_");
                            if !card_tags.contains(&processed_tag) {
                                card_tags.push(processed_tag);
                            }
                        }
                    }
                }
                // 获取卡片字段
                if let Some(fields_obj) = card["fields"].as_object() {
                    match card_type {
                        "qa" => {
                            // 问答卡片
                            fields.push(
                                fields_obj
                                    .get("question")
                                    .and_then(|v| v.as_str())
                                    .unwrap_or("")
                                    .to_string(),
                            );
                            fields.push("".to_string());
                            fields.push(
                                fields_obj
                                    .get("answer")
                                    .and_then(|v| v.as_str())
                                    .unwrap_or("")
                                    .to_string(),
                            );
                        }
                        "cloze" => {
                            // 填空卡片
                            let uid = ulid::Ulid::new().to_string();
                            if (cloze_mode == "mask_one_guess_one"
                                || cloze_mode == "mask_all_guess_one")
                                && is_per_cloze_per_card
                            {
                                let new_cloze_mode = format!("{}_multi", &cloze_mode);

                                // 获取问题文本
                                if let Some(question_text) =
                                    fields_obj.get("question").and_then(|v| v.as_str())
                                {
                                    // 使用正则表达式找出所有挖空
                                    let re = regex::Regex::new(r"\[\[c(\d+)::(.*?)\]\]").unwrap();
                                    let cloze_matches: Vec<_> =
                                        re.captures_iter(question_text).collect();

                                    // 如果找到挖空
                                    if !cloze_matches.is_empty() {
                                        // 对每个挖空创建一个卡片
                                        for captures in cloze_matches {
                                            let cloze_number =
                                                captures.get(1).map_or("1", |m| m.as_str());
                                            let cloze_tag = format!("c{}", cloze_number);

                                            // 创建该挖空的字段
                                            let mut cloze_fields = Vec::new();
                                            cloze_fields.push(format!(
                                                "{}-{}",
                                                uid.clone(),
                                                &cloze_number
                                            ));
                                            cloze_fields.push(question_text.to_string());

                                            // 添加答案字段
                                            let answer = fields_obj
                                                .get("answer")
                                                .and_then(|v| v.as_str())
                                                .unwrap_or("")
                                                .to_string();
                                            cloze_fields.push(answer);

                                            // 添加挖空模式
                                            cloze_fields.push(new_cloze_mode.clone());

                                            // 最后一个字段设为挖空序号
                                            cloze_fields.push(cloze_tag);

                                            // 创建Anki笔记并添加到笔记列表
                                            notes.push(crate::anki::models::AnkiNote {
                                                deck_name: parent_deck.to_string(),
                                                model_name: model_name.clone(),
                                                fields: cloze_fields,
                                                tags: Some(card_tags.clone()),
                                                guid: None,
                                            });
                                        }

                                        // 跳过下面的else块，因为已经创建了所有需要的卡片
                                        continue;
                                    } else {
                                        // 如果没有找到挖空或问题文本为空，使用默认处理方式
                                        fields.push(uid);
                                        fields.push(
                                            fields_obj
                                                .get("question")
                                                .and_then(|v| v.as_str())
                                                .unwrap_or("")
                                                .to_string(),
                                        );
                                        fields.push(
                                            fields_obj
                                                .get("answer")
                                                .and_then(|v| v.as_str())
                                                .unwrap_or("")
                                                .to_string(),
                                        );
                                        fields.push(cloze_mode.to_string());
                                        fields.push("".to_string());
                                    }
                                }
                            } else {
                                // 如果没有找到挖空或问题文本为空，使用默认处理方式
                                fields.push(uid);
                                fields.push(
                                    fields_obj
                                        .get("question")
                                        .and_then(|v| v.as_str())
                                        .unwrap_or("")
                                        .to_string(),
                                );
                                fields.push(
                                    fields_obj
                                        .get("answer")
                                        .and_then(|v| v.as_str())
                                        .unwrap_or("")
                                        .to_string(),
                                );
                                fields.push(cloze_mode.to_string());
                                fields.push("".to_string());
                            }
                        }
                        "choice" | "judge" | "multi_choice" => {
                            // 选择题或判断题
                            fields.push(
                                fields_obj
                                    .get("question")
                                    .and_then(|v| v.as_str())
                                    .unwrap_or("")
                                    .to_string(),
                            );
                            fields.push(
                                fields_obj
                                    .get("options")
                                    .and_then(|v| v.as_str())
                                    .unwrap_or("")
                                    .to_string(),
                            );
                            fields.push(
                                fields_obj
                                    .get("answer")
                                    .and_then(|v| v.as_str())
                                    .unwrap_or("")
                                    .to_string(),
                            );
                            fields.push(
                                fields_obj
                                    .get("remark")
                                    .and_then(|v| v.as_str())
                                    .unwrap_or("")
                                    .to_string(),
                            );
                            fields.push("".to_string());
                        }
                        _ => {}
                    }

                    // 创建Anki笔记
                    notes.push(crate::anki::models::AnkiNote {
                        deck_name: parent_deck.to_string(),
                        model_name: model_name.clone(),
                        fields,
                        tags: Some(card_tags),
                        guid: None,
                    });
                }
            }
        }
    }
    if is_transform_formula {
        // 处理所有笔记中的公式
        for note in &mut notes {
            for field in &mut note.fields {
                // 使用Dart端函数进行公式转换
                match crate::anki::utils::transform_formula(field).await {
                    Ok(transformed) => {
                        *field = transformed;
                    }
                    Err(e) => {
                        debug_print!("转换公式失败: {}", e);
                    }
                }
            }
        }
    }
    // 转义HTMl特殊字符
    if is_html_escape {
        for note in &mut notes {
            for field in &mut note.fields {
                *field = html_escape::encode_safe(field).to_string();
            }
        }
    }
    notes
}

/// 生成LLM卡片
pub async fn generate_cards(
    backend: &str,
    model: &str,
    api_key: &str,
    base_url: Option<String>,
    system_prompt: &str,
    user_prompt: &str,
    temperature: f32,
    max_tokens: Option<u32>,
    top_p: Option<f32>,
    top_k: Option<u32>,
    timeout: Option<u32>,
    parent_deck: &str,
    tags: &[String],
    card_type_models: &HashMap<String, String>,
    output_path: &str,
    attachments: Option<Vec<String>>,
    max_concurrent_requests: Option<usize>,
    logdir: &str,
    reasoning: Option<bool>,
    chunk_size: Option<usize>,
    page_range: &str,
    cloze_mode: &str,
    is_per_cloze_per_card: bool,
    is_transform_formula: bool,
    is_html_escape: bool,
    progress_callback_param: impl Fn(f64, f64, String) + Send + Sync + 'static,
) -> std::result::Result<(String, Vec<String>), String> {
    debug_print!("base_url: {:?}", &base_url);
    debug_print!("backend: {:?}", &backend);
    let progress_callback = Arc::new(progress_callback_param);

    // 发送进度更新
    progress_callback(10.0, 100.0, "初始化LLM...".to_string());

    // 构建LLM客户端
    let base_url_str = base_url.as_deref();
    let builder = build_llm_client(
        backend,
        model,
        api_key,
        base_url_str,
        system_prompt,
        temperature,
        max_tokens,
        top_p,
        top_k,
        reasoning,
        timeout,
    )
    .map_err(|e| e.to_string())?;

    // 准备用户提示
    progress_callback(20.0, 100.0, "准备提示内容...".to_string());

    // 处理附件
    let max_concurrent = max_concurrent_requests.unwrap_or(1).max(1); // 确保至少为1
    let attachment_batches = if let Some(paths) = attachments {
        process_attachments(
            paths,
            chunk_size.unwrap_or(3000),
            max_concurrent,
            Some(page_range),
        )
        .await
        .map_err(|e| e.to_string())?
    } else {
        vec![vec![]]
    };

    debug_print!("分批处理附件: {} 批", attachment_batches.len());
    progress_callback(
        30.0,
        100.0,
        format!("将处理 {} 批附件...", attachment_batches.len()),
    );

    let mut all_notes = Vec::new();
    let mut all_errors = Vec::new();
    let batch_count = attachment_batches.len();

    // 创建一个信号量来限制并发请求数
    let semaphore = Arc::new(tokio::sync::Semaphore::new(max_concurrent));

    // 为每批附件创建任务
    let mut tasks = Vec::new();
    for (batch_idx, batch) in attachment_batches.into_iter().enumerate() {
        // 每个任务需要一个新的LLM实例，因为LLM不是Clone
        let builder_clone = build_llm_client(
            backend,
            model,
            api_key,
            base_url_str,
            system_prompt,
            temperature,
            max_tokens,
            top_p,
            top_k,
            reasoning,
            timeout,
        )
        .map_err(|e| e.to_string())?;
        let llm = builder_clone
            .build()
            .map_err(|e| format!("构建LLM客户端失败: {}", e))?;

        let user_prompt = user_prompt.to_string();
        let semaphore_clone = semaphore.clone();
        let progress_callback_clone = progress_callback.clone();
        let batch_number = batch_idx + 1;
        let logdir = logdir.to_string();

        let task = async move {
            // 获取信号量许可
            let _permit = semaphore_clone.acquire().await.unwrap();

            // 更新进度
            progress_callback_clone(
                30.0 + (60.0 * batch_idx as f64 / batch_count as f64),
                100.0,
                format!("处理第 {}/{} 批附件...", batch_number, batch_count),
            );

            // 构建用户消息
            let mut messages = vec![];
            if !user_prompt.is_empty() {
                messages.push(ChatMessage::user().content(&user_prompt).build());
            }

            // 添加批次中的附件
            for path in &batch {
                let path_lower = path.to_lowercase();
                if path_lower.ends_with(".txt")
                    || path_lower.ends_with(".md")
                    || path_lower.ends_with(".html")
                {
                    // 读取文本文件内容
                    match fs::read_to_string(path) {
                        Ok(file_content) => {
                            // 添加文件内容为文本，并标明文件名
                            let file_name = Path::new(path)
                                .file_name()
                                .map_or("文件", |name| name.to_str().unwrap_or("文件"));

                            // 确定文件类型，用于代码块语法高亮
                            let file_type = if path_lower.ends_with(".md") {
                                "markdown"
                            } else if path_lower.ends_with(".html") {
                                "html"
                            } else {
                                "text"
                            };

                            // 将内容放在代码块中显示
                            let text_with_source = format!(
                                "以下是文件 {} 的内容:\n\n```{}\n{}\n```\n",
                                file_name, file_type, file_content
                            );
                            messages.push(ChatMessage::user().content(text_with_source).build());
                        }
                        Err(e) => {
                            // 文本文件读取失败，向上传递错误
                            return Err(format!("无法读取文本文件: {}", e));
                        }
                    }
                } else if path_lower.ends_with(".jpg") || path_lower.ends_with(".jpeg") {
                    let content = fs::read(path).expect("The file should exist");
                    messages.push(ChatMessage::user().image(ImageMime::JPEG, content).build());
                } else if path_lower.ends_with(".png") {
                    let content = fs::read(path).expect("The file should exist");
                    messages.push(ChatMessage::user().image(ImageMime::PNG, content).build());
                } else if path_lower.ends_with(".webp") {
                    let content = fs::read(path).expect("The file should exist");
                    messages.push(ChatMessage::user().image(ImageMime::WEBP, content).build());
                }
            }
            messages.push(
                ChatMessage::user()
                    .content("请根据上面内容生成卡片")
                    .build(),
            );

            // 发送请求到LLM
            let response_text = match llm.chat(&messages).await {
                Ok(response) => {
                    debug_print!("batch {} response_text received", batch_number);
                    response.text().unwrap_or_default()
                }
                Err(e) => return Err(format!("{}", e)),
            };

            // 如果提供了日志目录，则记录模型响应
            if !logdir.is_empty() {
                // 确保日志目录存在
                if !Path::new(&logdir).exists() {
                    if let Err(e) = fs::create_dir_all(&logdir) {
                        return Err(format!("无法创建日志目录: {}", e));
                    }
                }

                // 生成日志文件名
                let now = Local::now();
                let timestamp = now.format("%Y%m%d-%H%M%S");
                let log_filename = format!(
                    "[{}]-[{}]-batch{}.txt",
                    model.replace("/", "-"),
                    timestamp,
                    batch_number
                );
                let log_path = Path::new(&logdir).join(log_filename);

                // 写入模型响应
                match fs::File::create(&log_path) {
                    Ok(mut file) => {
                        if let Err(e) = file.write_all(response_text.as_bytes()) {
                            debug_print!("写入日志文件失败: {}", e);
                        }
                    }
                    Err(e) => {
                        debug_print!("创建日志文件失败: {}", e);
                    }
                }
            }

            // 解析JSON响应
            let extracted_json =
                extract_json(&response_text).unwrap_or_else(|_err| response_text.clone());
            debug_print!("batch {} extracted_json", batch_number);
            let fixed_json = crate::anki::utils::json_repair(&extracted_json)
                .await
                .map_err(|e| e.to_string())?;
            debug_print!("batch {} fixed_json: {}", batch_number, &fixed_json);

            // 解析为JSON
            let json_response: serde_json::Value = serde_json::from_str(&fixed_json)
                .map_err(|e| format!("无法解析LLM响应为JSON (批次 {}): {}", batch_number, e))?;

            Ok(json_response)
        };

        tasks.push(task);
    }

    // 执行所有任务并收集结果
    let results = join_all(tasks).await;

    // 处理每个批次的结果
    for (batch_idx, result) in results.into_iter().enumerate() {
        match result {
            Ok(json_response) => {
                // 解析卡片数据
                let batch_notes = parse_card_response(
                    &json_response,
                    parent_deck,
                    tags,
                    card_type_models,
                    cloze_mode,
                    is_per_cloze_per_card,
                    is_transform_formula,
                    is_html_escape,
                )
                .await;
                if !batch_notes.is_empty() {
                    all_notes.extend(batch_notes);
                } else {
                    let error_msg = format!("批次 {} 未返回有效卡片", batch_idx + 1);
                    debug_print!("{}", &error_msg);
                    all_errors.push(error_msg);
                }
            }
            Err(e) => {
                debug_print!("批次 {} 处理失败: {}", batch_idx + 1, e);
                all_errors.push(format!("批次 {} 处理失败: {}", batch_idx + 1, e));
            }
        }
    }

    if all_notes.is_empty() {
        if all_errors.is_empty() {
            return Err("未能从LLM响应中提取有效的卡片数据".to_string());
        } else {
            // 只显示前3条错误，其余写入桌面日志（仅桌面端）
            let display_errors: Vec<String> = all_errors.iter().take(3).cloned().collect();
            #[cfg(any(target_os = "macos", target_os = "windows", target_os = "linux"))]
            {
                let full_log_path = dirs::home_dir()
                    .map(|mut p| {
                        p.push("Desktop/anki_guru_error_log.txt");
                        p
                    })
                    .unwrap_or_else(|| std::path::PathBuf::from("anki_guru_error_log.txt"));
                if let Ok(mut file) = std::fs::File::create(&full_log_path) {
                    let _ = writeln!(file, "{}", all_errors.join("\n"));
                }
                let mut err_msg = format!("处理失败: {}", display_errors.join("; "));
                if all_errors.len() > 3 {
                    err_msg.push_str(&format!(
                        "; 更多错误信息已保存到: {}",
                        full_log_path.display()
                    ));
                }
                return Err(err_msg);
            }
            #[cfg(not(any(target_os = "macos", target_os = "windows", target_os = "linux")))]
            {
                // 移动端只显示前3条
                let err_msg = format!("处理失败: {}", display_errors.join("; "));
                return Err(err_msg);
            }
        }
    }

    // 生成APKG文件
    progress_callback(90.0, 100.0, "生成Anki卡片包...".to_string());
    let result_path =
        crate::anki::models::gen_apkg(all_notes, None, output_path, None, false, None)
            .await
            .map_err(|e| e.to_string())?;
    Ok((result_path, all_errors))
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::env;
}
