use anyhow::Result;
use dirs;
use reqwest::{Client, header::CONTENT_TYPE};
use rinf::debug_print;
use serde_json::{json, Value};
use std::env;
use std::path::PathBuf;
use std::time::Duration;
use thiserror::Error;
use crate::anki::utils::send_dart_request;
use ed25519_dalek::{Signature, Verifier, VerifyingKey};
use std::fs;
use chrono;

#[derive(Error, Debug)]
pub enum LicenseError {
    #[error("Network error: {0}")]
    NetworkError(#[from] reqwest::Error),
    #[error("IO error: {0}")]
    IoError(#[from] std::io::Error),
    #[error("JSON error: {0}")]
    JsonError(#[from] serde_json::Error),
    #[error("Device serial retrieval failed: {0}")]
    DeviceSerialError(String),
    #[error("License path error: {0}")]
    LicensePathError(String),
    #[error("Request failed: {0}")]
    RequestFailed(String),
}

/// Response structure for license requests
#[derive(Debug, Clone)]
pub struct LicenseResponse {
    pub status: String,
    pub message: String,
    pub data: Option<String>,
}

pub fn verify_signature(
    pubkey: &[u8],
    message: &[u8],
    signature: &[u8],
) -> Result<bool, Box<dyn std::error::Error>> {
    let verifying_key = VerifyingKey::from_bytes(pubkey.try_into()?)?;
    let sig = Signature::from_slice(signature)?;
    match verifying_key.verify(message, &sig) {
        Ok(_) => Ok(true),
        Err(_) => Ok(false),
    }
}

// pub fn read_license_file() -> Result<Value, Box<dyn std::error::Error>> {
//     let home = if cfg!(windows) {
//         std::env::var("USERPROFILE")?
//     } else {
//         std::env::var("HOME")?
//     };
//     let license_path = PathBuf::from(home).join(".pdf_guru").join("LICENSE.json");

//     let contents = fs::read_to_string(license_path)?;
//     let license: Value = serde_json::from_str(&contents)?;

//     Ok(license)
// }

pub fn verify_license_local(license_path: &str, current_machineid: &str) -> Result<bool, Box<dyn std::error::Error>> {
    // Auto-detect machine ID if the provided one is empty or whitespace-only
    let machine_id = if current_machineid.trim().is_empty() {
        // Use tokio::runtime to handle the async get_device_serial() call
        let rt = tokio::runtime::Runtime::new()
            .map_err(|e| Box::new(e) as Box<dyn std::error::Error>)?;

        rt.block_on(async {
            get_device_serial().await
                .map_err(|e| Box::new(e) as Box<dyn std::error::Error>)
        })?
    } else {
        current_machineid.to_string()
    };

    let contents = fs::read_to_string(license_path)?;
    let license: Value = serde_json::from_str(&contents)?;
    let pubkey =
        hex::decode("88e4d55c1b2b7d5d9121a0fadb7ae17aac2cbb926e284de42adcb10bff749530").unwrap();
    let obj = license.as_object().unwrap();
    let salt = "kevin2li-anki";
    let message = format!(
        "{}.{}.{}.{}.{}.{}.{}.{:.6}.{}.{}",
        obj.get("code").and_then(Value::as_str).unwrap_or(""),
        obj.get("productid").and_then(Value::as_str).unwrap_or(""),
        obj.get("product_version").and_then(Value::as_str).unwrap_or(""),
        obj.get("can_upgrade").and_then(Value::as_bool).unwrap_or(false),
        obj.get("machineid").and_then(Value::as_str).unwrap_or(""),
        obj.get("issue_time").and_then(Value::as_str).unwrap_or(""),
        obj.get("device_count").and_then(Value::as_u64).unwrap_or(0),
        obj.get("duration").and_then(Value::as_f64).unwrap_or(0.0),
        obj.get("function_mode").and_then(Value::as_u64).unwrap_or(0),
        salt,
    );
    // println!("Message: {}", message);
    let digest = md5::compute(message.as_bytes());
    let message_bytes = digest.0;
    // println!("digest: {:#?}", digest);
    let signature = obj.get("sign").and_then(Value::as_str).unwrap_or("");
    let signature_bytes = hex::decode(signature)?;
    // 验证签名
    let res = verify_signature(&pubkey, &message_bytes, &signature_bytes)?;
    if !res {
        println!("License verification failed: Invalid signature");
        return Ok(false);
    }

    // 验证是否过期
    let issue_time = obj.get("issue_time").and_then(Value::as_str).unwrap_or("");
    let duration = obj.get("duration").and_then(Value::as_f64).unwrap_or(0.0);
    println!("License issue time: {}", issue_time);

    // Parse issue time
    let issue_datetime = chrono::DateTime::parse_from_rfc3339(issue_time)
        .map_err(|e| Box::new(e) as Box<dyn std::error::Error>)?;

    // Calculate expiration time
    let expiration = issue_datetime + chrono::Duration::seconds((duration * 3600.0) as i64);
    println!("License expiration time: {}", expiration);

    // Compare with current time
    let now = chrono::Utc::now();
    println!("Current time: {}", now);
    if now > expiration {
        println!("License verification failed: License expired (current: {}, expiration: {})", now, expiration);
        return Ok(false);
    }

    // 验证设备一致性
    let license_machine_id = obj.get("machineid").and_then(Value::as_str).unwrap_or("");
    println!("License machine ID: {}", license_machine_id);
    println!("Current machine ID: {}", machine_id);
    if license_machine_id != machine_id {
        println!("License verification failed: Machine ID mismatch (license: {}, current: {})", license_machine_id, machine_id);
        return Ok(false);
    }

    println!("License verification passed: All checks successful");
    Ok(true)
}

/// Get device serial number for the current platform
pub async fn get_device_serial() -> Result<String, LicenseError> {
    #[cfg(target_os = "android")]
    {
        // For Android, call back to Dart to get the device ID
        match send_dart_request(json!({}), "get_mobile_device_id".to_string()).await {
            Ok(response) => {
                if response.status == "success" {
                    Ok(response.data)
                } else {
                    Err(LicenseError::DeviceSerialError(format!(
                        "Failed to get Android device ID: {}",
                        response.message
                    )))
                }
            }
            Err(e) => Err(LicenseError::DeviceSerialError(format!(
                "Failed to communicate with Dart for Android device ID: {}",
                e
            ))),
        }
    }

    #[cfg(target_os = "ios")]
    {
        // For iOS, call back to Dart to get the device ID
        match send_dart_request(json!({}), "get_mobile_device_id".to_string()).await {
            Ok(response) => {
                if response.status == "success" {
                    Ok(response.data)
                } else {
                    Err(LicenseError::DeviceSerialError(format!(
                        "Failed to get iOS device ID: {}",
                        response.message
                    )))
                }
            }
            Err(e) => Err(LicenseError::DeviceSerialError(format!(
                "Failed to communicate with Dart for iOS device ID: {}",
                e
            ))),
        }
    }

    #[cfg(target_os = "macos")]
    {
        match get_machineid::id() {
            Ok(machine_id) => Ok(machine_id),
            Err(e) => Err(LicenseError::DeviceSerialError(format!(
                "Failed to get machine ID: {}",
                e
            ))),
        }
    }

    #[cfg(target_os = "windows")]
    {
        match get_machineid::id() {
            Ok(machine_id) => Ok(machine_id),
            Err(e) => Err(LicenseError::DeviceSerialError(format!(
                "Failed to get machine ID: {}",
                e
            ))),
        }
    }

    #[cfg(target_os = "linux")]
    {
        match get_machineid::id() {
            Ok(machine_id) => Ok(machine_id),
            Err(e) => Err(LicenseError::DeviceSerialError(format!(
                "Failed to get machine ID: {}",
                e
            ))),
        }
    }

    #[cfg(not(any(
        target_os = "android",
        target_os = "ios",
        target_os = "macos",
        target_os = "windows",
        target_os = "linux"
    )))]
    {
        Err(LicenseError::DeviceSerialError(
            "Unsupported platform".to_string(),
        ))
    }
}

/// Get the license file path for the current platform
pub fn get_license_path() -> Result<String, LicenseError> {
    let external_dir = get_platform_specific_dir()?;
    let license_path = PathBuf::from(external_dir).join("LICENSE.json");
    Ok(license_path.to_string_lossy().to_string())
}

/// Get platform-specific directory for license storage
fn get_platform_specific_dir() -> Result<String, LicenseError> {
    #[cfg(target_os = "android")]
    {
        // For Android, use a fallback directory
        // In a real implementation, this would use platform-specific APIs
        if let Some(cache_dir) = dirs::cache_dir() {
            Ok(cache_dir.join(".pdf_guru").to_string_lossy().to_string())
        } else {
            Ok("/data/local/tmp/.pdf_guru".to_string())
        }
    }

    #[cfg(target_os = "ios")]
    {
        // For iOS, use a fallback directory
        // In a real implementation, this would use platform-specific APIs
        if let Some(cache_dir) = dirs::cache_dir() {
            Ok(cache_dir.join(".pdf_guru").to_string_lossy().to_string())
        } else {
            Ok("/tmp/.pdf_guru".to_string())
        }
    }

    #[cfg(target_os = "macos")]
    {
        let home = env::var("HOME").map_err(|_| {
            LicenseError::LicensePathError("Failed to get HOME environment variable".to_string())
        })?;
        Ok(PathBuf::from(home)
            .join(".pdf_guru")
            .to_string_lossy()
            .to_string())
    }

    #[cfg(target_os = "windows")]
    {
        let user_profile = env::var("USERPROFILE").map_err(|_| {
            LicenseError::LicensePathError(
                "Failed to get USERPROFILE environment variable".to_string(),
            )
        })?;
        Ok(PathBuf::from(user_profile)
            .join(".pdf_guru")
            .to_string_lossy()
            .to_string())
    }

    #[cfg(target_os = "linux")]
    {
        let home = env::var("HOME").map_err(|_| {
            LicenseError::LicensePathError("Failed to get HOME environment variable".to_string())
        })?;
        Ok(PathBuf::from(home)
            .join(".pdf_guru")
            .to_string_lossy()
            .to_string())
    }

    #[cfg(not(any(
        target_os = "android",
        target_os = "ios",
        target_os = "macos",
        target_os = "windows",
        target_os = "linux"
    )))]
    {
        Err(LicenseError::LicensePathError(
            "Unsupported platform".to_string(),
        ))
    }
}

/// Get current platform string
fn get_platform_string() -> String {
    #[cfg(target_os = "android")]
    return "android".to_string();

    #[cfg(target_os = "ios")]
    return "ios".to_string();

    #[cfg(target_os = "macos")]
    return "macos".to_string();

    #[cfg(target_os = "windows")]
    return "windows".to_string();

    #[cfg(target_os = "linux")]
    return "linux".to_string();

    #[cfg(not(any(
        target_os = "android",
        target_os = "ios",
        target_os = "macos",
        target_os = "windows",
        target_os = "linux"
    )))]
    return "unknown".to_string();
}

/// Make activation request to the license server
pub async fn request_activate(url: &str, code: &str, machineid: Option<&str>) -> Result<LicenseResponse, LicenseError> {
    // Determine which machine ID to use
    let machine_id = match machineid {
        Some(id) if !id.is_empty() => id.to_string(),
        _ => {
            // Either None or empty string, so get device serial automatically
            get_device_serial().await?
        }
    };

    if machine_id.is_empty() {
        return Ok(LicenseResponse {
            status: "exception".to_string(),
            message: "获取机器码失败".to_string(),
            data: None,
        });
    }
    debug_print!("machine_id: {}", machine_id);
    let client = Client::builder()
        .timeout(Duration::from_secs(5))
        .build()?;

    let platform = get_platform_string();
    let payload = json!({
        "machineid": machine_id,
        "code": code,
        "platform": platform,
    });

    match client
        .post(url)
        .header(CONTENT_TYPE, "application/json")
        .json(&payload)
        .send()
        .await
    {
        Ok(response) => {
            let response_data: Value = response.json().await?;

            if response_data["status"] == "success" {
                // Save license data to file
                let license_path = get_license_path()?;
                let license_data = response_data["data"].as_str().unwrap_or("");
                std::fs::write(&license_path, license_data)?;

                Ok(LicenseResponse {
                    status: "success".to_string(),
                    message: "激活成功".to_string(),
                    data: Some(license_data.to_string()),
                })
            } else {
                Ok(LicenseResponse {
                    status: "error".to_string(),
                    message: response_data["message"].as_str().unwrap_or("Unknown error").to_string(),
                    data: None,
                })
            }
        }
        Err(e) => Ok(LicenseResponse {
            status: "exception".to_string(),
            message: e.to_string(),
            data: None,
        }),
    }
}

/// Make unregister request to the license server
pub async fn request_unregister(url: &str, machineid: Option<&str>) -> Result<LicenseResponse, LicenseError> {
    let license_path = get_license_path()?;

    // Check if license file exists
    if !std::path::Path::new(&license_path).exists() {
        return Ok(LicenseResponse {
            status: "error".to_string(),
            message: "License file not found".to_string(),
            data: None,
        });
    }

    // Determine which machine ID to use
    let machine_id = match machineid {
        Some(id) if !id.is_empty() => id.to_string(),
        _ => {
            // Either None or empty string, so get device serial automatically
            get_device_serial().await?
        }
    };

    let license_content = std::fs::read_to_string(&license_path)?;
    let license_data: Value = serde_json::from_str(&license_content)?;

    let client = Client::builder()
        .timeout(Duration::from_secs(5))
        .build()?;

    let payload = json!({
        "machineid": machine_id,
        "code": license_data["code"],
    });

    match client
        .post(url)
        .header(CONTENT_TYPE, "application/json")
        .json(&payload)
        .send()
        .await
    {
        Ok(response) => {
            let response_data: Value = response.json().await?;

            if response_data["status"] == "success" {
                // Delete license file
                std::fs::remove_file(&license_path)?;

                Ok(LicenseResponse {
                    status: "success".to_string(),
                    message: "注销成功！".to_string(),
                    data: None,
                })
            } else {
                Ok(LicenseResponse {
                    status: "error".to_string(),
                    message: response_data["message"].as_str().unwrap_or("Unknown error").to_string(),
                    data: None,
                })
            }
        }
        Err(e) => Ok(LicenseResponse {
            status: "exception".to_string(),
            message: e.to_string(),
            data: None,
        }),
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_get_device_serial() {
        let result = get_device_serial().await;
        // Should not panic and should return a result
        assert!(result.is_ok() || result.is_err());

        // On supported platforms, should return a string (may be empty for mobile)
        if let Ok(serial) = result {
            println!("Device serial: {}", serial);
        }
    }

    #[test]
    fn test_get_license_path() {
        let result = get_license_path();
        assert!(result.is_ok());

        if let Ok(path) = result {
            println!("License path: {}", path);
            assert!(path.contains(".pdf_guru"));
            assert!(path.ends_with("LICENSE.json"));
        }
    }

    #[test]
    fn test_verify_license_local_with_empty_machineid() {
        // Test that verify_license_local handles empty machine ID gracefully
        // This test will fail due to missing license file, but should not panic
        // and should attempt to auto-detect machine ID

        let non_existent_path = "/tmp/non_existent_license.json";

        // Test with empty string
        let result = verify_license_local(non_existent_path, "");
        // Should return an error due to missing file, not due to empty machine ID
        assert!(result.is_err());

        // Test with whitespace-only string
        let result = verify_license_local(non_existent_path, "   ");
        // Should return an error due to missing file, not due to empty machine ID
        assert!(result.is_err());

        // Test with valid machine ID (should still fail due to missing file)
        let result = verify_license_local(non_existent_path, "test-machine-id");
        // Should return an error due to missing file
        assert!(result.is_err());
    }

    #[test]
    fn test_get_platform_string() {
        let platform = get_platform_string();
        assert!(!platform.is_empty());
        println!("Platform: {}", platform);

        // Should be one of the supported platforms
        assert!(["android", "ios", "macos", "windows", "linux", "unknown"].contains(&platform.as_str()));
    }

    #[tokio::test]
    async fn test_request_activate_invalid_url_auto_machineid() {
        let result = request_activate("http://invalid-url-that-does-not-exist.com", "test-code", None).await;

        // The function should handle errors gracefully and return a LicenseResponse
        match result {
            Ok(response) => {
                // Should return an exception status due to network error
                assert_eq!(response.status, "exception");
            }
            Err(e) => {
                // If it returns an error, it should be a device serial error (empty machine ID)
                println!("Expected error for invalid URL test (auto machine ID): {}", e);
                // This is acceptable for the test
            }
        }
    }

    #[tokio::test]
    async fn test_request_activate_invalid_url_provided_machineid() {
        let result = request_activate("http://invalid-url-that-does-not-exist.com", "test-code", Some("test-machine-id")).await;

        // The function should handle errors gracefully and return a LicenseResponse
        match result {
            Ok(response) => {
                // Should return an exception status due to network error
                assert_eq!(response.status, "exception");
            }
            Err(e) => {
                // If it returns an error, it should be a network error
                println!("Expected error for invalid URL test (provided machine ID): {}", e);
                // This is acceptable for the test
            }
        }
    }

    #[tokio::test]
    async fn test_request_activate_empty_machineid_fallback() {
        // Test that empty string machine ID falls back to auto-detection
        let result = request_activate("http://invalid-url-that-does-not-exist.com", "test-code", Some("")).await;

        // The function should handle errors gracefully and return a LicenseResponse
        match result {
            Ok(response) => {
                // Should return an exception status due to network error or empty machine ID
                assert!(response.status == "exception");
            }
            Err(e) => {
                // If it returns an error, it should be a device serial error (empty machine ID)
                println!("Expected error for empty machine ID test: {}", e);
                // This is acceptable for the test
            }
        }
    }

    #[tokio::test]
    async fn test_request_unregister_no_license() {
        // This should fail gracefully when no license file exists
        let result = request_unregister("http://test.com", None).await;

        match result {
            Ok(response) => {
                // Should return an error status since no license file exists
                assert_eq!(response.status, "error");
                assert!(response.message.contains("License file not found"));
            }
            Err(e) => {
                // If it returns an error, it should be a device serial or path error
                println!("Expected error for no license test: {}", e);
                // This is acceptable for the test
            }
        }
    }

    #[tokio::test]
    async fn test_request_unregister_with_machineid() {
        // Test with provided machine ID
        let result = request_unregister("http://test.com", Some("test-machine-id")).await;

        match result {
            Ok(response) => {
                // Should return an error status since no license file exists
                assert_eq!(response.status, "error");
                assert!(response.message.contains("License file not found"));
            }
            Err(e) => {
                // If it returns an error, it should be a path error (not device serial error)
                println!("Expected error for no license test with machine ID: {}", e);
                // This is acceptable for the test
            }
        }
    }
}

