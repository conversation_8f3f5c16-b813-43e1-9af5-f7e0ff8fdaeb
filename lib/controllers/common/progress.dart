
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:share_plus/share_plus.dart';
import 'dart:async';
import 'package:path/path.dart' as p;
import 'package:open_file_manager/open_file_manager.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:open_file/open_file.dart';



class ProgressController extends GetxController {
  final settingController = Get.find<SettingController>();
  // 进度条
  final RxDouble current = 0.0.obs;
  final RxDouble total = 100.0.obs;
  final RxString message = ''.obs;
  final RxString parentMessage = ''.obs;
  final RxString status =
      'pending'.obs; // pending | running/processing | completed | error
  final RxString outputPath = ''.obs;

  // 添加开始时间属性
  final Rx<DateTime?> startTime = Rx<DateTime?>(null);
  final RxString elapsedTime = '0s'.obs;

  // 添加定时器用于更新用时
  Timer? _timer;

  // 显示相关
  final isShowProgressDialog = false.obs;
  final isShowOutputHint = false.obs;
  final actionButtonsNumber = 0.obs;

  void reset(
      {bool? showDialog = true,
      int? numberButtons = 2,
      bool? showOutputHint = false}) {
    current.value = 0;
    total.value = 100;
    message.value = '';
    parentMessage.value = '';
    status.value = 'pending';
    startTime.value = null;
    elapsedTime.value = '0s';
    isShowProgressDialog.value = showDialog ?? true;
    isShowOutputHint.value = showOutputHint ?? false;
    actionButtonsNumber.value = numberButtons ?? 2;
    _timer?.cancel();
  }


  void start() {
    startTime.value = DateTime.now();
    // 创建定时器每秒更新用时
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (startTime.value != null) {
        final seconds = DateTime.now().difference(startTime.value!).inSeconds;
        elapsedTime.value = TimeUtils.formatDuration(seconds * 1000);
      }
    });
  }

  @override
  void onClose() {
    _timer?.cancel();
    super.onClose();
  }

  void showProgressDialog(context) async {
    if (!isShowProgressDialog.value) {
      return;
    }
    start();
    showShadDialog(
      barrierDismissible: false,
      context: context,
      builder: (context) {
        return const ProgressDialog();
      },
    );
  }

  Future<void> playAudio() async {
    final player = AudioPlayer();
    await player.play(DeviceFileSource(outputPath.value));
  }

  Future<void> shareOutputFile({BuildContext? context}) async {
    try {
      final p = PathUtils(outputPath.value);
      if (p.isDir) {
        logger.e('File is a directory: ${outputPath.value}');
        throw Exception('progress.error.shareDirectoryNotSupported'.tr);
      }
      if (!p.exists()) {
        logger.e('File does not exist: ${outputPath.value}');
        throw Exception('progress.error.filePathNotExist'.tr);
      }

      logger.d('Attempting to share file: ${outputPath.value}');

      if (Platform.isIOS || Platform.isMacOS) {
        // 获取分享按钮的位置和大小
        if (context == null) {
          logger.e('Context is null');
          throw Exception('progress.error.cannotGetContext'.tr);
        }

        final RenderBox? box = context.findRenderObject() as RenderBox?;
        if (box == null) {
          logger.e('RenderBox is null');
          throw Exception('progress.error.cannotGetButtonPosition'.tr);
        }

        final Rect sharePositionOrigin =
            box.localToGlobal(Offset.zero) & box.size;
        logger.d('Share position origin: $sharePositionOrigin');

        try {
          final result = await Share.shareXFiles(
            [XFile(outputPath.value)],
            subject: 'Anki Deck',
            sharePositionOrigin: sharePositionOrigin,
          );
          logger.d('Share result: ${result.status}');
        } catch (e) {
          logger.e('Share.shareXFiles error: $e');
          rethrow;
        }
      } else {
        // Android sharing
        final result = await Share.shareXFiles(
          [XFile(outputPath.value)],
          subject: 'Anki Deck',
        );
        logger.d('Share result: ${result.status}');
      }
    } catch (e) {
      logger.e('Error in openOutputFile: $e');
      showToastNotification(context, '${'progress.error.cannotShareFile'.tr}: ${e.toString()}', '',
          type: "error");
    }
  }

  Future<void> openOutputFile({BuildContext? context}) async {
    try {
      // option1:
      // if (PathUtils.isMobile) {
      //   openFileManager(
      //     androidConfig: AndroidConfig(
      //       folderType: AndroidFolderType.other,
      //       folderPath: PathUtils(outputPath.value).parent,
      //     ),
      //     iosConfig: IosConfig(
      //       folderPath: 'Export',
      //     ),
      //   );
      // } else if (PathUtils.isDesktop) {
      //   // await openFileBySystemDefault(outputPath.value);
      //   await OpenFile.open(outputPath.value);
      // }

      // option2:
      await OpenFile.open(outputPath.value);
    } catch (e) {
      logger.e('Error in openOutputFile: $e');
      showToastNotification(context, '${'progress.error.cannotOpenFile'.tr}: ${e.toString()}', '',
          type: "error");
    }
  }

  Future<void> openOutputDir({BuildContext? context}) async {
    try {
      final p = PathUtils(outputPath.value);
      if (!p.exists()) {
        logger.e('File does not exist: ${outputPath.value}');
        return;
      }
      if (Platform.isWindows) {
        var path = "";
        if (p.isDir) {
          path = outputPath.value;
        } else {
          path = p.parent;
        }
        await Process.run("explorer.exe", ["/select,", path], runInShell: true);
      } else if (Platform.isMacOS) {
        var path = "";
        if (p.isDir) {
          path = outputPath.value;
        } else {
          path = p.parent;
        }
        await Process.run("open", [path], runInShell: true);
      } else if (Platform.isIOS || Platform.isAndroid) {
        openFileManager(
          androidConfig: AndroidConfig(
            folderType: AndroidFolderType.other,
            folderPath: PathUtils(outputPath.value).parent,
          ),
          iosConfig: IosConfig(
            folderPath: 'Export',
          ),
        );
      }
    } catch (e) {
      logger.e('Error in openOutputDir: $e');
      Get.snackbar(
        'progress.error.title'.tr,
        '${'progress.error.cannotOpenFolder'.tr}: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red[100],
        duration: const Duration(seconds: 2),
        icon: const Icon(Icons.error, color: Colors.red),
      );
    }
  }

  void updateProgress({String status = "completed", String message = "", double current = 100.0, double total = 100.0}) {
    if (status == "completed" || status == "error") {
      _timer?.cancel();
    }
    if (status.isNotEmpty) {
      this.status.value = status;
    }
    if (current.isFinite && current >= 0) {
      this.current.value = current.isFinite ? current : 0.0;
    }
    if (total.isFinite && total > 0) {
      if (status == "completed") {
        this.total.value = 100.0;
      } else {
        this.total.value = total.isFinite ? total : 100.0;
      }
    }
    if (message.isNotEmpty) {
      this.message.value = message;
    }
  }
}

// 提取为单独的组件
class ProgressDialog extends GetView<ProgressController> {
  const ProgressDialog({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: ShadDialog(
        title: Text("progress.dialog.title".tr),
        alignment: Alignment.center,
        radius: BorderRadius.circular(8),
        backgroundColor: theme.colorScheme.background,
        border: Border.all(color: theme.colorScheme.border),
        shadows: ShadShadows.lg,
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          spacing: 12,
          children: [
            const ProgressContent(),
            const TimeInfo(),
            Obx(() => controller.status.value == "completed"
                ? const ActionButtons()
                : const SizedBox.shrink()),
          ],
        ),
      ),
    );
  }
}

class TimeInfo extends GetView<ProgressController> {
  const TimeInfo({super.key});

  @override
  Widget build(BuildContext context) {
    final time = controller.startTime.value;
    final timeStr = time != null
        ? "${time.hour.toString().padLeft(2, '0')}:"
            "${time.minute.toString().padLeft(2, '0')}:"
            "${time.second.toString().padLeft(2, '0')}"
        : "--:--:--";

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "progress.time.startTime".tr,
              style: const TextStyle(
                fontSize: 12,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              timeStr,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "progress.time.elapsedTime".tr,
              style: const TextStyle(
                fontSize: 12,
              ),
            ),
            const SizedBox(height: 4),
            Obx(() => Text(
                  controller.elapsedTime.value,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                )),
          ],
        ),
      ],
    );
  }
}

class ProgressContent extends GetView<ProgressController> {
  const ProgressContent({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Text(
              "progress.status.totalProgress".tr,
              style: const TextStyle(
                fontSize: 14,
              ),
            ),
            const SizedBox(width: 4),
            Expanded(
              child: Obx(() => Text(
                    controller.parentMessage.value,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 2,
                  )),
            ),
          ],
        ),
        const SizedBox(height: 10),
        Obx(() {
          final status = controller.status.value;
          switch (status) {
            case 'pending':
              return const PendingState();
            case 'running':
              return const RunningState();
            case 'processing':
              return const RunningState();
            case 'completed':
              return const CompletedState();
            case 'error':
              return const ErrorState();
            default:
              return const SizedBox();
          }
        }),
        const SizedBox(height: 10),
      ],
    );
  }
}

class ProgressBar extends StatelessWidget {
  final double value;
  final Color? valueColor;

  const ProgressBar({
    super.key,
    required this.value,
    this.valueColor,
  });

  @override
  Widget build(BuildContext context) {
    double safeValue = value.isFinite ? value.clamp(0.0, 1.0) : 0.0;
    return ConstrainedBox(
      constraints: BoxConstraints(
        maxWidth: MediaQuery.sizeOf(context).width,
      ),
      child: ShadProgress(
        value: safeValue,
        color: valueColor,
        minHeight: 6,
      ),
    );
  }
}

class PendingState extends StatelessWidget {
  const PendingState({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            const Icon(Icons.lock_clock, size: 16),
            const SizedBox(width: 8),
            Text("progress.status.initializing".tr),
          ],
        ),
        const SizedBox(height: 8),
        ProgressBar(
            value: 0.01, valueColor: ShadTheme.of(context).colorScheme.primary),
      ],
    );
  }
}

class RunningState extends GetView<ProgressController> {
  const RunningState({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            const SizedBox(
              height: 18,
              width: 18,
              child: CircularProgressIndicator(
                strokeWidth: 2,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Obx(() => Text(
                    controller.message.value.isEmpty
                        ? "progress.status.processing".tr
                        : controller.message.value,
                    softWrap: true,
                    overflow: TextOverflow.visible,
                    style: const TextStyle(fontSize: 12),
                  )),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Obx(() => ProgressBar(
              value: controller.current.value / controller.total.value,
            )),
      ],
    );
  }
}

class CompletedState extends GetView<ProgressController> {
  const CompletedState({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Icon(
              Icons.check_circle,
              color: Colors.green[600],
              size: 16,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Obx(() => Text(
                    controller.message.value.isEmpty ? "progress.status.completed".tr : controller.message.value,
                    style: TextStyle(
                      color: Colors.green[600],
                      fontSize: 12,
                    ),
                    softWrap: true,
                    overflow: TextOverflow.visible,
                  )),
            ),
          ],
        ),
        const SizedBox(height: 8),
        ProgressBar(value: 1.0, valueColor: Colors.green[600]),
        const SizedBox(height: 12),
        if (Platform.isIOS ||
            Platform.isAndroid ||
            controller.isShowOutputHint.value)
          Obx(() => Text(
                "${'progress.status.fileSavedTo'.tr}:${Platform.isIOS ? p.basename(controller.outputPath.value) : controller.outputPath.value}",
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
                softWrap: true,
                overflow: TextOverflow.visible,
              )),
      ],
    );
  }
}

class ActionButtons extends GetView<ProgressController> {
  const ActionButtons({super.key});

  @override
  Widget build(BuildContext context) {
    if (controller.actionButtonsNumber.value == 0) {
      return const SizedBox();
    } else if (controller.actionButtonsNumber.value == 1) {
      return Row(
        children: [
          Expanded(
            child: ShadButton(
              onPressed: () {
                if (controller.outputPath.value.isNotEmpty) {
                  if (PathUtils(controller.outputPath.value).isDir) {
                    controller.openOutputDir(context: context);
                  } else {
                    controller.openOutputFile(context: context);
                  }
                } else {
                  logger.e("outputPath is empty");
                }
              },
              child: Text("progress.button.open".tr),
            ),
          ),
        ],
      );
    } else if (controller.actionButtonsNumber.value == 2) {
      return Row(
        children: [
          Expanded(
            child: ShadButton(
              onPressed: () {
                // controller.openOutputDir(context: context);
                controller.openOutputFile(context: context);
              },
              child: Text("progress.button.open".tr),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: ShadButton.secondary(
              onPressed: () {
                controller.shareOutputFile(context: context);
              },
              child: Text("progress.button.share".tr),
            ),
          ),
        ],
      );
    } else if (controller.actionButtonsNumber.value == 3) {
      return Row(
        children: [
          Expanded(
            child: ShadButton(
              onPressed: () {
                controller.openOutputFile(context: context);
              },
              child: Text("progress.button.open".tr),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: ShadButton.secondary(
              onPressed: () {
                controller.shareOutputFile(context: context);
              },
              child: Text("progress.button.share".tr),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: ShadButton.secondary(
              onPressed: () {
                controller.playAudio();
              },
              child: Text("progress.button.play".tr),
            ),
          ),
        ],
      );
    }
    return const SizedBox.shrink();
  }
}

class ErrorState extends GetView<ProgressController> {
  const ErrorState({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Icon(
              Icons.error,
              color: Colors.red[600],
              size: 16,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Obx(() => Text(
                    controller.message.value,
                    style: TextStyle(color: Colors.red[600], fontSize: 12),
                    softWrap: true,
                    overflow: TextOverflow.visible,
                  )),
            ),
          ],
        ),
        const SizedBox(height: 8),
        ProgressBar(value: 1.0, valueColor: Colors.red[600]),
      ],
    );
  }
}
