import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:anki_guru/controllers/anki/text_card.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:anki_guru/pages/common.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'dart:io';

class QAForm extends GetView<TextCardFormController> {
  const QAForm({super.key});

  @override
  Widget build(context) {
    final ankiConnectController = Get.find<AnkiConnectController>();
    return ShadCard(
      padding: const EdgeInsets.only(left: 16, right: 16, top: 16, bottom: 16),
      footer: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Expanded(
            child: ShadButton(
              size: ShadButtonSize.lg,
              onPressed: () {
                controller.submit(context);
              },
              child: Text('toolbox.common.submit'.tr),
            ),
          )
        ],
      ),
      child: Obx(
        () => Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          spacing: 4,
          children: [
            ShadSelectWithInput(
              key: ValueKey(
                  "deck-${ankiConnectController.parentDeckList.length}"),
              label: 'anki.common.target_deck'.tr,
              placeholder: 'anki.placeholder.target_deck_search_input'.tr,
              searchPlaceholder: 'anki.target_deck_search_placeholder',
              isMultiple: false,
              initialValue: [controller.parentDeck.value],
              options: ankiConnectController.parentDeckList
                  .map((e) => {'value': e, 'label': e})
                  .toList(),
              onChanged: (value) {
                logger.i(value);
                controller.parentDeck.value = value.single;
              },
              onAddNew: (newDeckName) {
                // Add to the deck list if not already present
                if (!ankiConnectController.parentDeckList.contains(newDeckName)) {
                  ankiConnectController.parentDeckList.add(newDeckName);
                }

                // Set as selected deck
                controller.parentDeck.value = newDeckName;
              },
              hasSuffix: true,
              onRefresh: () async {
                logger.i("refresh");
                final result =
                    await ankiConnectController.resetAnkiConnectData();
                if (result) {
                  showToastNotification(
                      context, 'anki.common.refresh_success'.tr, "");
                }
              },
            ),
            ShadSwitchCustom(
              key: ValueKey("subdeck-${controller.isCreateSubDeck.hashCode}"),
              label: 'anki.common.create_subdeck'.tr,
              initialValue: controller.isCreateSubDeck.value,
              onChanged: (v) {
                controller.isCreateSubDeck.value = v;
              },
            ),
            if (controller.isCreateSubDeck.value)
              ShadSelectWithInput(
                label: 'anki.text_card.subdeck_prefix'.tr,
                placeholder: 'anki.text_card.select_subdeck_prefix'.tr,
                searchPlaceholder: 'anki.text_card.input_subdeck_prefix'.tr,
                isMultiple: false,
                initialValue: [controller.subDeckPrefix.value],
                options: controller.subDeckPrefixList,
                onChanged: (value) {
                  logger.i(value);
                  controller.subDeckPrefix.value = value.single;
                },
                onAddNew: (newPrefix) {
                  // Add new sub deck prefix to the list if not already present
                  final newOption = {'value': newPrefix, 'label': newPrefix};
                  if (!controller.subDeckPrefixList.any((option) => option['value'] == newPrefix)) {
                    controller.subDeckPrefixList.add(newOption);
                  }

                  // Set as selected prefix
                  controller.subDeckPrefix.value = newPrefix;
                },
              ),
            ShadRadioGroupCustom(
              label: 'anki.common.card_mode'.tr,
              initialValue: controller.qaParams.mode.value,
              items: controller.qaModelList,
              onChanged: (value) {
                controller.qaParams.mode.value = value;
              },
            ),
            ShadSelectCustom(
              label: 'anki.text_card.card_template'.tr,
              placeholder: 'anki.text_card.select_card_template'.tr,
              initialValue: [controller.cardModel.value],
              options: ankiConnectController.modelList
                  .map((e) => {"value": e, "label": e})
                  .toList(),
              onChanged: (value) {
                controller.cardModel.value = value.single;
                controller.updateFieldList(controller.cardModel.value);
              },
            ),
            ShadFieldMappingTable(
              key: ValueKey(
                  "field_table_${controller.cardModel.value}_${ankiConnectController.fieldList.length}_qa"),
              fieldList: ankiConnectController.fieldList,
              optionsList: {
                "Front": controller.questionRegexList.toList(),
                "Back": controller.qaAnswerRegexList.toList(),
                "Hint": controller.hintRegexList.toList(),
              },
              defaultOptionsList: controller.commonRegexList,
              cardModel: controller.cardModel.value,
              onUpdateFieldMapping: (field, patternMatch) {
                controller.updateFieldMapping(field, patternMatch);
              },
              getFieldMappingValue: (field) {
                return controller.getFieldMappingValue(field);
              },
            ),
            if (ankiConnectController.fieldList.isNotEmpty) ...[
              if (controller.qaParams.mode.value == "cross_file") ...[
                ShadSelectCustom(
                  label: 'anki.text_card.question_file_fields'.tr,
                  placeholder: 'anki.text_card.select_question_file_fields'.tr,
                  initialValue: controller.qMainFieldList,
                  isMultiple: true,
                  options: ankiConnectController.fieldList
                      .map((e) => {'value': e, 'label': e})
                      .toList(),
                  onChanged: (value) {
                    controller.qMainFieldList.value = value;
                  },
                  onValidate: (value) async {
                    logger.i(value);
                    if (value.isEmpty) {
                      return 'anki.text_card.select_question_file_fields_required'.tr;
                    }
                    return "";
                  },
                  onValidateError: (error) {
                    controller.qaParams.errorFieldMapping['qMainFieldList'] =
                        error;
                  },
                ),
                ShadSelectCustom(
                  label: 'anki.text_card.answer_file_fields'.tr,
                  placeholder: 'anki.text_card.select_answer_file_fields'.tr,
                  initialValue: controller.aMainFieldList,
                  isMultiple: true,
                  options: ankiConnectController.fieldList
                      .map((e) => {'value': e, 'label': e})
                      .toList(),
                  onChanged: (value) {
                    controller.aMainFieldList.value = value;
                  },
                  onValidate: (value) async {
                    if (value.isEmpty) {
                      return 'anki.text_card.select_answer_file_fields_required'.tr;
                    }
                    return "";
                  },
                  onValidateError: (error) {
                    controller.qaParams.errorFieldMapping['aMainFieldList'] =
                        error;
                  },
                ),
              ],
            ],
            ShadRadioGroupCustom(
              label: 'anki.text_card.document_type'.tr,
              initialValue: controller.docType.value,
              items: controller.docTypeList,
              onChanged: (value) {
                controller.docType.value = value;
              },
            ),
            if (controller.docType.value == "md") ...[
              ShadSwitchCustom(
                label: 'anki.text_card.obsidian_syntax'.tr,
                initialValue: controller.isObsidian.value,
                onChanged: (v) {
                  controller.isObsidian.value = v;
                },
              ),
              ShadInputWithFileSelect(
                title: 'anki.text_card.media_file_directory'.tr,
                placeholder: Text('anki.text_card.media_folder_placeholder'.tr),
                initialValue: [controller.mediaFolder.value],
                isFolder: true,
                onFilesSelected: (value) {
                  controller.mediaFolder.value = value.single;
                },
                onValidate: (value, files) async {
                  return await validateOutputDir(value, files);
                },
                onValidateError: (error) {},
              ),
            ],
            if (controller.cardModel.value == "Kevin Text QA Card v2" &&
                controller.qaParams.mode.value == "same_file") ...[
              ShadSwitchCustom(
                label: 'anki.text_card.question_single_line'.tr,
                initialValue: controller.qaParams.isQuestionSingleLine.value,
                onChanged: (v) {
                  controller.qaParams.isQuestionSingleLine.value = v;
                },
              )
            ],
            ShadSwitchCustom(
              label: 'anki.text_card.answer_cloze'.tr,
              initialValue: controller.qaParams.isAnswerCloze.value,
              onChanged: (v) {
                controller.qaParams.isAnswerCloze.value = v;
              },
            ),
            if (controller.qaParams.isAnswerCloze.value) ...[
              ShadCheckboxGroup(
                label: 'anki.text_card.cloze_grammar'.tr,
                initialValues: controller.qaParams.clozeGrammar.toList(),
                items: controller.clozeGrammarList,
                onChanged: (value) {
                  logger.i(value);
                  controller.qaParams.clozeGrammar.value = value;
                },
                onValidate: (value) async {
                  if (value.isEmpty) {
                    return 'anki.text_card.at_least_one_cloze_grammar'.tr;
                  }
                  return "";
                },
                onValidateError: (error) {},
              ),
            ],
            ShadSelectWithInput(
              key: ValueKey("tags-${ankiConnectController.tagsList.hashCode}"),
              label: 'anki.common.tags'.tr,
              placeholder: 'anki.text_card.select_tags'.tr,
              searchPlaceholder: 'anki.text_card.input_tags'.tr,
              isMultiple: true,
              initialValue: controller.tags.toList(),
              options: ankiConnectController.tagsList
                  .map((e) => {'value': e, 'label': e})
                  .toList(),
              onChanged: (value) {
                logger.i(value);
                controller.tags.value = value;
              },
              onAddNew: (newTag) {
                // Add new tag to the tags list if not already present
                if (!ankiConnectController.tagsList.contains(newTag)) {
                  ankiConnectController.tagsList.add(newTag);
                }

                // Add to selected tags if not already present
                if (!controller.tags.contains(newTag)) {
                  controller.tags.add(newTag);
                }
              },
            ),
            if (controller.qaParams.mode.value != 'cross_file')
              ShadInputWithFileSelect(
                key: const ValueKey('input-file'),
                title: 'toolbox.common.inputFile'.tr,
                placeholder: Text('toolbox.common.inputFilePlaceholder'.tr),
                allowedExtensions: const ['txt', 'md', 'docx'],
                isRequired: true,
                allowMultiple: true,
                enableDragSort: true,
                onFilesSelected: (files) {
                  logger.i(files);
                  controller.selectedFilePaths.value = files;
                },
                onValidate: (value, files) async {
                  return await validateFile(value, files);
                },
                onValidateError: (error) {},
              ),
            if (controller.qaParams.mode.value == 'cross_file') ...[
              ShadInputWithFileSelect(
                key: const ValueKey('q_file_input_qa'),
                title: 'anki.common.q_file'.tr,
                placeholder: Text('anki.placeholder.select_q_file'.tr),
                allowedExtensions: const ['txt', 'md', 'docx'],
                isRequired: true,
                onFilesSelected: (files) {
                  controller.qFile.value = files.single;
                },
                onValidate: (value, files) async {
                  return await validateFile(value, files);
                },
                onValidateError: (error) {},
              ),
              ShadInputWithFileSelect(
                key: const ValueKey('a_file_input_qa'),
                title: 'anki.common.a_file'.tr,
                placeholder: Text('anki.text_card.select_answer_file_placeholder'.tr),
                allowedExtensions: const ['txt', 'md', 'docx'],
                isRequired: true,
                onFilesSelected: (files) {
                  controller.aFile.value = files.single;
                },
                onValidate: (value, files) async {
                  return await validateFile(value, files);
                },
                onValidateError: (error) {},
              ),
            ],
          ],
        ),
      ),
    );
  }
}
