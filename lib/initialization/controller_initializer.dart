import 'package:get/get.dart';
import 'package:anki_guru/controllers/common.dart';
import 'package:anki_guru/controllers/anki/ocr/ocr_controller.dart';
import 'package:anki_guru/controllers/video_notes/subtitle_controller.dart';
import 'package:anki_guru/controllers/video_notes/video_note_controller.dart';
import 'package:anki_guru/controllers/anki/pdf_note.dart';
import 'package:anki_guru/controllers/toolbox/bookmark.dart';

/// Controller initialization module
/// Handles GetX controller initialization in the correct dependency order
class ControllerInitializer {
  /// Initialize all GetX controllers in the correct order
  /// Returns key controllers that are referenced later in main()
  static ControllerReferences initializeControllers() {
    logger.i('Initializing controllers...');
    
    // Initialize core controllers first (other controllers may depend on these)
    final settingController = Get.put(SettingController());
    final messageController = Get.put(MessageController());
    final progressController = Get.put(ProgressController());

    // Initialize UI and interaction controllers early (WebviewController depends on ClipboardController)
    Get.put(ClipboardController());
    Get.put(ImageCardController());

    // Initialize communication controllers (these depend on UI controllers)
    final webviewController = Get.put(WebviewController());
    Get.put(WebSocketManager());

    // Initialize Anki-related controllers
    final ankiConnectController = Get.put(AnkiConnectController());
    Get.put(AnkiSyncController());

    // Initialize media and content controllers (these depend on WebviewController)
    Get.put(SubtitleController());
    Get.put(VideoNoteController());
    Get.put(PDFNoteController());
    Get.put(PDFBookmarkPageController());

    // Initialize processing controllers
    Get.put(OCRController());
    
    logger.i('Controllers initialized');
    
    return ControllerReferences(
      settingController: settingController,
      messageController: messageController,
      progressController: progressController,
      ankiConnectController: ankiConnectController,
      webviewController: webviewController,
    );
  }
}

/// Container for controller references that are needed later in initialization
class ControllerReferences {
  final SettingController settingController;
  final MessageController messageController;
  final ProgressController progressController;
  final AnkiConnectController ankiConnectController;
  final WebviewController webviewController;
  
  const ControllerReferences({
    required this.settingController,
    required this.messageController,
    required this.progressController,
    required this.ankiConnectController,
    required this.webviewController,
  });
}
